require 'net/http'
require 'json'

class RegistrationsController < Devise::RegistrationsController
  def create
    # Verify reCAPTCHA if enabled
    if Rails.application.credentials.recaptcha&.dig(:secret_key).present? && !verify_recaptcha
      Rails.logger.error "reCAPTCHA verification failed for email: #{params.dig(:user, :email)}"
      flash[:alert] ||= I18n.t('recaptcha.verification_failed')
      redirect_to new_user_registration_path
      return
    end
    
    super do |resource|
      if resource.persisted? && session[:locale].present?
        # The user_profile is automatically created by a callback on the User model.
        # We can safely assume it exists here.
        resource.user_profile.update(default_language: session[:locale])
      end
    end
  end

  private

  def verify_recaptcha
    return true unless Rails.application.credentials.recaptcha&.dig(:secret_key).present?
    
    token = params[:recaptcha_token]
    
    if token.blank?
      Rails.logger.error "reCAPTCHA token is blank"
      flash[:alert] = I18n.t('recaptcha.token_blank')
      return false
    end

    # In development, allow bypass for domain issues
    if Rails.env.development? && token.present?
      Rails.logger.info "Development environment: bypassing reCAPTCHA"
      return true
    end

    Rails.logger.info "Verifying reCAPTCHA token (length: #{token.length})"
    
    uri = URI('https://www.google.com/recaptcha/api/siteverify')
    response = Net::HTTP.post_form(uri, {
      'secret' => Rails.application.credentials.recaptcha[:secret_key],
      'response' => token,
      'remoteip' => request.remote_ip
    })

    Rails.logger.info "reCAPTCHA API response code: #{response.code}"
    
    result = JSON.parse(response.body)
    
    Rails.logger.info "reCAPTCHA result: success=#{result['success']}, score=#{result['score']}, action=#{result['action']}, hostname=#{result['hostname']}, challenge_ts=#{result['challenge_ts']}"
    
    if result['error-codes'].present?
      Rails.logger.error "reCAPTCHA error codes: #{result['error-codes'].join(', ')}"
      
      # Handle specific error codes with user-friendly messages
      if result['error-codes'].include?('timeout-or-duplicate')
        flash[:alert] = I18n.t('recaptcha.timeout_or_duplicate')
      end
    end
    
    # For reCAPTCHA v3, check both success and score
    # Note: score can be 0.0 for very suspicious traffic, which is valid but should fail
    success = result['success'] == true && result['score'].to_f >= 0.3
    
    unless success
      Rails.logger.error "reCAPTCHA verification failed: success=#{result['success']}, score=#{result['score']}"
    end
    
    success
  rescue => e
    Rails.logger.error "reCAPTCHA verification error: #{e.message}"
    Rails.logger.error e.backtrace.first(5).join("\n")
    false
  end
end
