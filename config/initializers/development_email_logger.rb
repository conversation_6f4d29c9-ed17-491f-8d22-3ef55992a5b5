# config/initializers/development_email_logger.rb
class DevelopmentEmailLogger
  def self.delivering_email(message)
    body_content = if message.multipart? && message.text_part
                     message.text_part.body.decoded
                   else
                     message.body.decoded
                   end
    # Puts the decoded body directly to the log, nothing else.
    puts body_content
  end
end
