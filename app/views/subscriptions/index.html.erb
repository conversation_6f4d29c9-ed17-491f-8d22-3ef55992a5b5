<%# ABOUTME: Subscription plans selection page with Free and Premium tiers %>
<%# ABOUTME: Handles referral code validation and dynamic price display via Javascript %>
<div class="subscription-container">
  <h1><%= t('subscriptions.index.title') %></h1>
  
  <!-- Stripe Coupon Section -->
  <% unless @has_active_premium %>
    <div class="coupon-section">
      <div class="coupon-input-group">
        <input type="text" 
               id="coupon-code-input" 
               class="coupon-code-input" 
               placeholder="<%= t('subscriptions.index.coupon_placeholder') %>" 
               autocomplete="off">
        <button id="apply-coupon-btn" class="apply-coupon-btn">
          <%= t('subscriptions.index.apply_coupon') %>
        </button>
      </div>
      <div id="coupon-banner" class="coupon-banner" style="display: none;">
        <span id="coupon-message" class="banner-text"></span>
        <button id="remove-coupon-btn" class="remove-coupon-btn" style="display: none; margin-left: 10px; background: #dc3545; color: white; border: none; padding: 4px 12px; border-radius: 4px; cursor: pointer; font-size: 0.9em;">
          <%= t('subscriptions.coupon.remove') %>
        </button>
      </div>
    </div>
  <% end %>

  <div class="subscription-plans">
  
    <!-- Standard Plan -->
    <div class="plan-card standard-plan">
      <div class="plan-header">
        <div class="plan-title">
          <h2><%= t('subscriptions.plans.standard.name') %></h2>
          
          <% if @monthly_price && @annual_price %>
            <div class="billing-toggle-container">
              <div class="billing-toggle">
                <button class="toggle-option monthly-option active" data-period="monthly">
                  <%= t('subscriptions.billing.monthly') %>
                </button>
                <button class="toggle-option annual-option" data-period="annual">
                  <%= t('subscriptions.billing.yearly') %>
                </button>
              </div>
            </div>
          <% end %>
        </div>
      </div>
      
      <div class="plan-price">
        <div id="regular-price-display">
          <!-- Main Price View -->
          <div id="main-price-view">
            <div class="regular-price">
              <% if @monthly_price %>
                <span class="currency" id="standard-currency"><%= @monthly_price[:currency] == 'EUR' ? '€' : @monthly_price[:currency] %></span>
                <span class="amount" id="standard-amount">
                  <%= @monthly_price[:amount].floor %>
                </span>
                <span class="period" id="standard-period">
                  <%= t('subscriptions.plans.standard.period_month') %>
                </span>
              <% else %>
                <!-- Fallback display when Stripe is not configured -->
                <span class="currency"><%= t('subscriptions.plans.standard.currency') %></span>
                <span class="amount">--</span>
                <span class="period"><%= t('subscriptions.plans.standard.period_month') %></span>
              <% end %>
            </div>
          </div>
          <!-- Discount preview -->
          <div id="discount-preview" style="display: none;">
            <div class="regular-price">
              <span class="currency" id="discounted-currency"></span>
              <span class="amount" id="discounted-amount"></span>
              <span class="period" id="discounted-period"></span>
              <span class="original-price">
                <span id="original-currency"></span><span id="original-amount"></span>
              </span>
            </div>
          </div>
          <!-- Unlocked price preview -->
          <div id="unlocked-price-preview" style="display: none;">
            <div class="regular-price">
              <span class="currency" id="unlocked-currency"></span>
              <span class="amount" id="unlocked-amount"></span>
              <span class="period" id="unlocked-period"></span>
              <span class="original-price">
              <span id="unlocked-original-currency"></span><span id="unlocked-original-amount"></span>
              </span>
            </div>
            <div>
              <span class="unlocked-price-nickname" id="unlocked-price-description"></span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="plan-features">
        <div class="feature-item included">
          <%= heroicon "check-circle", variant: :solid, options: { class: "feature-icon", style: "color: #3B82F6;" } %>
          <span><%= t('subscriptions.plans.standard.features.feat_1') %></span>
        </div>
        <div class="feature-item included">
          <%= heroicon "check-circle", variant: :solid, options: { class: "feature-icon", style: "color: #3B82F6;" } %>
          <span><%= t('subscriptions.plans.standard.features.feat_2') %></span>
        </div>
        <div class="feature-item included">
          <%= heroicon "check-circle", variant: :solid, options: { class: "feature-icon", style: "color: #3B82F6;" } %>
          <span><%= t('subscriptions.plans.standard.features.feat_3') %></span>
        </div>
        <div class="feature-item included">
          <%= heroicon "check-circle", variant: :solid, options: { class: "feature-icon", style: "color: #3B82F6;" } %>
          <span><%= t('subscriptions.plans.standard.features.feat_4') %></span>
        </div>
      </div>
      
      <div class="plan-action">
        <% if @has_active_premium %>
          <button class="plan-button standard-button inactive-plan" disabled>
            <%= t('subscriptions.plans.standard.current_plan') %>
          </button>
        <% else %>
          <button class="plan-button standard-button" 
                  data-plan="standard" 
                  data-stripe-price-id="<%= @monthly_price ? @monthly_price[:price_id] : '' %>"
                  data-monthly-price-id="<%= @monthly_price ? @monthly_price[:price_id] : '' %>"
                  data-annual-price-id="<%= @annual_price ? @annual_price[:price_id] : '' %>"
                  <%= 'disabled' unless @monthly_price || @annual_price %>>
            <%= t('subscriptions.plans.standard.button') %>
          </button>
        <% end %>
      </div>
    </div>

    <!-- Free Plan -->
    <div class="plan-card free-plan">
      <div class="plan-header">
        <h2><%= t('subscriptions.plans.free.name') %></h2>
      </div>
      
      <div class="plan-price">
        <div class="regular-price">
          <span class="currency"><%= t('subscriptions.plans.free.currency') %></span>
          <span class="amount"><%= t('subscriptions.plans.free.price') %></span>
          <span class="period"><%= t('subscriptions.plans.free.period') %></span>
        </div>
      </div>
      
      <div class="plan-features">
        <div class="feature-item excluded">  
          <span><%= t('subscriptions.plans.free.features.limited_features') %></span>
        </div>
        <div class="feature-item excluded">
          <%= heroicon "x-circle", variant: :solid, options: { class: "feature-icon", style: "color: gray;" } %>
          <span><%= t('subscriptions.plans.free.features.feat_1') %></span>
        </div>
        <div class="feature-item excluded">
          <%= heroicon "x-circle", variant: :solid, options: { class: "feature-icon", style: "color: gray;" } %>
          <span><%= t('subscriptions.plans.free.features.feat_2') %></span>
        </div>
        <div class="feature-item excluded">
          <%= heroicon "x-circle", variant: :solid, options: { class: "feature-icon", style: "color: gray;" } %>
          <span><%= t('subscriptions.plans.free.features.feat_3') %></span>
        </div>
      </div>
      
      <div class="plan-action">
        <% unless @has_active_premium %>
          <button class="plan-button free-button" disabled>
            <%= t('subscriptions.plans.free.button') %>
          </button>
        <% end %>
      </div>
    </div>

  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Get DOM elements
  const standardButton = document.querySelector('.standard-button');
  const toggleOptions = document.querySelectorAll('.toggle-option');
  const amountSpan = document.getElementById('standard-amount');
  const periodSpan = document.getElementById('standard-period');
  const couponInput = document.getElementById('coupon-code-input');
  const applyCouponBtn = document.getElementById('apply-coupon-btn');
  const couponBanner = document.getElementById('coupon-banner');
  const couponMessage = document.getElementById('coupon-message');
  const removeCouponBtn = document.getElementById('remove-coupon-btn');
  
  // Price display containers with unique IDs
  const mainPriceView = document.getElementById('main-price-view');
  const discountPreviewDiv = document.getElementById('discount-preview');
  const unlockedPricePreviewDiv = document.getElementById('unlocked-price-preview');
  
  // Store price data
  const monthlyPrice = Math.floor(parseFloat('<%= @monthly_price ? @monthly_price[:amount] : 0 %>'));
  const annualPrice = Math.floor(parseFloat('<%= @annual_price ? @annual_price[:amount] : 0 %>'));
  const monthlyPriceId = '<%= @monthly_price ? @monthly_price[:price_id] : "" %>';
  const annualPriceId = '<%= @annual_price ? @annual_price[:price_id] : "" %>';
  const currency = '<%= @monthly_price ? (@monthly_price[:currency] == "EUR" ? "€" : @monthly_price[:currency]) : "€" %>';
  
  // State variables
  let appliedCouponCode = null;
  let appliedCouponData = null;
  
  // Load saved preference from localStorage
  const savedPlan = localStorage.getItem('preferredPlan') || 'monthly';
  
  // Initialize with saved preference
  if (savedPlan === 'annual' && toggleOptions.length > 1) {
    toggleOptions[0].classList.remove('active');
    toggleOptions[1].classList.add('active');
    updatePricing('annual');
  } else {
    updatePricing('monthly');
  }
  
  // Add click handlers to toggle buttons
  toggleOptions.forEach(option => {
    option.addEventListener('click', function() {
      toggleOptions.forEach(opt => opt.classList.remove('active'));
      this.classList.add('active');
      const period = this.dataset.period;
      updatePricing(period);
      localStorage.setItem('preferredPlan', period);
    });
  });
  
  // Function to update pricing display
  function updatePricing(period) {
    if (!amountSpan || !periodSpan) return;
    
    let currentPrice = 0;
    
    if (period === 'annual') {
      currentPrice = annualPrice;
      amountSpan.textContent = annualPrice;
      periodSpan.textContent = '<%= t('subscriptions.plans.standard.period_year') %>';
      if (standardButton) standardButton.dataset.stripePriceId = annualPriceId;
    } else {
      currentPrice = monthlyPrice;
      amountSpan.textContent = monthlyPrice;
      periodSpan.textContent = '<%= t('subscriptions.plans.standard.period_month') %>';
      if (standardButton) standardButton.dataset.stripePriceId = monthlyPriceId;
    }
    
    // Reset all views first
    mainPriceView.style.display = 'block';
    discountPreviewDiv.style.display = 'none';
    unlockedPricePreviewDiv.style.display = 'none';
    
    if (appliedCouponData) {
      if (appliedCouponData.unlocked_price_id) {
        showUnlockedPrice(currentPrice, period);
      } else if (appliedCouponData.discount) {
        calculateAndShowDiscount(currentPrice, period);
      }
    }
  }
  
  // Function to show an unlocked special price
  function showUnlockedPrice(originalPrice, period) {
    const unlockedDetails = appliedCouponData.unlocked_price_details;
    
    document.querySelector('.billing-toggle-container').style.display = 'none';
    
    // Populate nickname/description if it exists
    const descriptionSpan = document.getElementById('unlocked-price-description');
    if (unlockedDetails.nickname) {
      descriptionSpan.textContent = unlockedDetails.nickname;
      descriptionSpan.style.display = 'block'; // Or 'inline'
    } else {
      descriptionSpan.style.display = 'none';
    }
    
    const unlockedCurrency = unlockedDetails.currency === 'eur' ? '€' : unlockedDetails.currency.toUpperCase();
    document.getElementById('unlocked-currency').textContent = unlockedCurrency;
    document.getElementById('unlocked-amount').textContent = unlockedDetails.amount;
    document.getElementById('unlocked-period').textContent = unlockedDetails.interval === 'year' ? 
      '<%= t('subscriptions.plans.standard.period_year') %>' : 
      '<%= t('subscriptions.plans.standard.period_month') %>';
      
    document.getElementById('unlocked-original-currency').textContent = currency;
    document.getElementById('unlocked-original-amount').textContent = originalPrice;
    
    // Show unlocked preview, hide others
    mainPriceView.style.display = 'none';
    discountPreviewDiv.style.display = 'none';
    unlockedPricePreviewDiv.style.display = 'block';
    
    if (standardButton) {
      standardButton.dataset.stripePriceId = appliedCouponData.unlocked_price_id;
    }
  }

  // Function to calculate and display a standard discounted price
  function calculateAndShowDiscount(originalPrice, period) {
    let discountedPrice = originalPrice;
    
    if (appliedCouponData.discount.type === 'percentage') {
      discountedPrice = Math.floor(originalPrice * (1 - appliedCouponData.discount.value / 100));
    } else if (appliedCouponData.discount.type === 'amount') {
      discountedPrice = Math.floor(Math.max(0, originalPrice - appliedCouponData.discount.value));
    }
    
    document.getElementById('original-currency').textContent = currency;
    document.getElementById('original-amount').textContent = originalPrice;
    
    document.getElementById('discounted-currency').textContent = currency;
    document.getElementById('discounted-amount').textContent = discountedPrice;
    document.getElementById('discounted-period').textContent = period === 'annual' ? 
      '<%= t('subscriptions.plans.standard.period_year') %>' : 
      '<%= t('subscriptions.plans.standard.period_month') %>';
    
    // Show discount preview, hide others
    mainPriceView.style.display = 'none';
    discountPreviewDiv.style.display = 'block';
    unlockedPricePreviewDiv.style.display = 'none';
  }
  
  // Event listener for standard button
  if (standardButton) {
    standardButton.addEventListener('click', function(e) {
      e.preventDefault();
      const priceId = standardButton.dataset.stripePriceId;
      if (!priceId || priceId === '') {
        alert('Stripe pricing is not configured.');
        return;
      }
      
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = '<%= subscriptions_path %>';
      
      const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
      
      const csrfInput = document.createElement('input');
      csrfInput.type = 'hidden';
      csrfInput.name = 'authenticity_token';
      csrfInput.value = csrfToken;
      form.appendChild(csrfInput);
      
      const priceInput = document.createElement('input');
      priceInput.type = 'hidden';
      priceInput.name = 'price_id';
      priceInput.value = priceId;
      form.appendChild(priceInput);
      
      if (appliedCouponCode) {
        const couponCodeInput = document.createElement('input');
        couponCodeInput.type = 'hidden';
        couponCodeInput.name = 'coupon_code';
        couponCodeInput.value = appliedCouponCode;
        form.appendChild(couponCodeInput);
      }
      
      document.body.appendChild(form);
      form.submit();
    });
  }
  
  // Coupon validation functionality
  function validateCoupon() {
    const code = couponInput.value.trim();
    if (!code) {
      showCouponMessage('Please enter a coupon code', 'error');
      return;
    }
    
    fetch('<%= validate_coupon_subscriptions_path %>', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
        'Accept': 'application/json'
      },
      body: JSON.stringify({ code: code })
    })
    .then(response => response.json())
    .then(data => {
      if (data.valid) {
        appliedCouponCode = code.toUpperCase();
        appliedCouponData = data;
        showCouponMessage(data.message, 'success');
        
        const activeToggle = document.querySelector('.toggle-option.active');
        const currentPeriod = activeToggle ? activeToggle.dataset.period : 'monthly';
        updatePricing(currentPeriod);
      } else {
        removeCoupon();
        showCouponMessage(data.message || 'Invalid coupon code.', 'error');
      }
    })
    .catch(error => {
      console.error('Error validating coupon:', error);
      removeCoupon();
      showCouponMessage('Unable to validate coupon', 'error');
    });
  }
  
  function showCouponMessage(message, type) {
    couponMessage.textContent = message;
    couponBanner.className = 'coupon-banner ' + type;
    couponBanner.style.display = 'block';
    
    if (type === 'error') {
      if (removeCouponBtn) removeCouponBtn.style.display = 'none';
    } else if (type === 'success') {
      if (removeCouponBtn) removeCouponBtn.style.display = 'inline-block';
    }
  }
  
  function removeCoupon() {
    appliedCouponCode = null;
    appliedCouponData = null;
    couponInput.value = '';
    couponBanner.style.display = 'none';
    if (removeCouponBtn) removeCouponBtn.style.display = 'none';
    
    document.querySelector('.billing-toggle-container').style.display = 'block';
    
    const activeToggle = document.querySelector('.toggle-option.active');
    const currentPeriod = activeToggle ? activeToggle.dataset.period : 'monthly';
    updatePricing(currentPeriod);
    
    // Explicitly reset views
    mainPriceView.style.display = 'block';
    discountPreviewDiv.style.display = 'none';
    unlockedPricePreviewDiv.style.display = 'none';
  }
  
  // Event listeners for coupon
  if (applyCouponBtn) {
    applyCouponBtn.addEventListener('click', e => { e.preventDefault(); validateCoupon(); });
  }
  
  if (couponInput) {
    couponInput.addEventListener('keypress', e => {
      if (e.key === 'Enter') { e.preventDefault(); validateCoupon(); }
    });
  }
  
  if (removeCouponBtn) {
    removeCouponBtn.addEventListener('click', e => { e.preventDefault(); removeCoupon(); });
  }
});
</script>