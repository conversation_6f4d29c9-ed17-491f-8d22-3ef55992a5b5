source "https://rubygems.org"
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby "3.1.2"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 7.0.8", ">= *******"

# The original asset pipeline for Rails [https://github.com/rails/sprockets-rails]
gem "sprockets-rails"

# Use postgresql as the database for Active Record
gem "pg", "~> 1.1"

# Use the Puma web server [https://github.com/puma/puma]
gem "puma", "~> 5.0"

# Use Redis adapter to run Action Cable in production
# gem "redis", "~> 4.0"

# Use Kredis to get higher-level data types in Redis [https://github.com/rails/kredis]
# gem "kredis"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
# gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ mingw mswin x64_mingw jruby ]

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Use Sass to process CSS
# gem "sassc-rails"

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
gem "image_processing", "~> 1.2"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri mingw x64_mingw ]
  
  # Testing framework
  gem 'factory_bot_rails'
end

group :test do
  # Browser testing for feature specs
  gem 'capybara'
  gem 'capybara-playwright-driver'
  gem 'cuprite'  # Modern headless driver
  
  # Test utilities
  gem 'database_cleaner-active_record'
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem "web-console"

  # Speed up commands on slow machines / big apps [https://github.com/rails/spring]
  # gem "spring"
  # 
  # Add speed badges [https://github.com/MiniProfiler/rack-mini-profiler]
  gem 'rack-mini-profiler'
  gem 'memory_profiler'

  gem 'heapy'
  
  #gem "propshaft", ">= 0.7.0"
  #gem "render_async"
  #gem "sitemap_generator"
  #gem 'solid_queue'
  #gem "stimulus-rails"
  #gem "webdrivers"
  #gem 'zeitwerk'
end

gem 'vite_rails'
gem "rspec-rails", "~> 7.1"

gem "rails-i18n"
gem "devise-i18n"
gem 'sassc-rails'

gem "devise", "~> 4.9"
gem "action_policy", "~> 0.7.3"

gem "aws-sdk-s3", "~> 1.177"

gem 'resend', '0.15.0'

gem 'devise_invitable'

gem 'active_storage_validations'

# Process manager for running web + worker in single service
gem 'foreman'

gem 'heroicon'

gem 'pagy', '~> 9.3'

gem 'geocoder'
gem 'rack-attack'

# gem 'scout_apm'

gem 'phonelib'

# Background jobs processing
gem 'good_job'

# JWT for secure token generation
gem 'jwt', '~> 2.7'
gem "i18n-js", "~> 4.2"

# Payment processing
gem 'stripe', '~> 13.3'
