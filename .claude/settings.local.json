{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["mcp__gmail__gmail_send_email", "Bash(bundle exec rspec:*)", "Bash(git add:*)", "WebSearch", "mcp__linear__linear_getTeams", "mcp__linear__linear_getWorkflowStates", "mcp__linear__linear_createIssue", "Bash(git reset:*)", "<PERSON><PERSON>(git clean:*)", "Bash(git checkout:*)", "Bash(git pull:*)", "mcp__linear__linear_getIssueById", "mcp__linear__linear_updateIssue", "Bash(git commit:*)", "Bash(git push:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__gemini__chat", "<PERSON><PERSON>(gh issue view:*)", "mcp__stripe__list_products", "mcp__stripe__list_prices", "mcp__linear__linear_getIssues"], "deny": [], "ask": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["linear"], "outputStyle": "default"}