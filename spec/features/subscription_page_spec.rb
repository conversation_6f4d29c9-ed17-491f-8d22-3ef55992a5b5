# ABOUTME: Feature specs for the subscription page UI and referral code functionality
# ABOUTME: Tests user interactions with plan selection and discount application

require 'rails_helper'

RSpec.describe 'Subscription Page', type: :feature, js: true do
  let(:user) { create(:user) }
  let!(:free_plan) { create(:plan, tier: 'free', name: 'Free Plan', price_cents: 0, interval: 'month') }
  let!(:premium_plan) { create(:plan, tier: 'premium', name: 'Premium Plan', price_cents: 2999, interval: 'month', stripe_price_id: 'price_test_123') }

  before do
    sign_in user
  end

  describe 'viewing subscription plans' do
    before do
      visit subscriptions_path
    end

    it 'displays the page title' do
      expect(page).to have_content('Vyberte si váš plán')
    end

    it 'shows both Free and Premium plans' do
      expect(page).to have_css('.plan-card.free-plan')
      expect(page).to have_css('.plan-card.premium-plan')
    end

    it 'displays correct pricing for Free plan' do
      within('.free-plan') do
        expect(page).to have_content('Zadarmo')
        expect(page).to have_content('€0/mesiac')
      end
    end

    it 'displays correct pricing for Premium plan' do
      within('.premium-plan') do
        expect(page).to have_content('Premium')
        expect(page).to have_content('€29,99/mesiac')
      end
    end

    it 'shows feature lists for both plans' do
      within('.free-plan') do
        expect(page).to have_css('.feature-item.included', count: 3)
        expect(page).to have_css('.feature-item.excluded', count: 2)
      end

      within('.premium-plan') do
        expect(page).to have_css('.feature-item.included', count: 5)
      end
    end

    it 'has referral code input field' do
      expect(page).to have_css('#referral-code-input')
      expect(page).to have_button('Použiť')
    end
  end

  describe 'applying referral codes' do
    before do
      visit subscriptions_path
    end

    context 'with valid code FREEYEAR' do
      it 'applies discount and shows success banner' do
        fill_in 'referral-code-input', with: 'FREEYEAR'
        click_button 'Použiť'

        expect(page).to have_css('.referral-success-banner', visible: true)
        expect(page).to have_content('Unlisters Premium na celý rok bezplatne!')
        
        within('.premium-plan') do
          expect(page).to have_css('.original-price', visible: true)
          expect(page).to have_content('FREE')
        end
      end

      it 'changes apply button to applied state' do
        fill_in 'referral-code-input', with: 'FREEYEAR'
        click_button 'Použiť'

        expect(page).to have_button('Aplikované')
        expect(page).to have_css('.apply-referral-btn.applied')
      end

      it 'highlights premium plan' do
        fill_in 'referral-code-input', with: 'FREEYEAR'
        click_button 'Použiť'

        expect(page).to have_css('.premium-plan.highlighted')
      end
    end

    context 'with invalid code' do
      it 'shows error for invalid code' do
        fill_in 'referral-code-input', with: 'INVALIDCODE'
        click_button 'Použiť'

        expect(page).to have_content('Neplatný referral kód')
        expect(page).to have_css('.invalid-code')
      end

      it 'does not apply any discount' do
        fill_in 'referral-code-input', with: 'INVALIDCODE'
        click_button 'Použiť'

        expect(page).not_to have_css('.referral-success-banner', visible: true)
        within('.premium-plan') do
          expect(page).not_to have_content('FREE')
        end
      end
    end

    context 'with empty code' do
      it 'resets any applied discount' do
        # First apply a valid code
        fill_in 'referral-code-input', with: 'FREEYEAR'
        click_button 'Použiť'
        expect(page).to have_css('.referral-success-banner', visible: true)

        # Then clear and apply empty
        fill_in 'referral-code-input', with: ''
        click_button 'Použiť'

        expect(page).not_to have_css('.referral-success-banner', visible: true)
        expect(page).not_to have_css('.premium-plan.highlighted')
      end
    end
  end

  describe 'plan selection' do
    before do
      visit subscriptions_path
    end

    it 'marks free plan as selected by default' do
      expect(page).to have_css('.free-button.selected')
    end

    it 'allows selecting free plan' do
      find('.free-button').click
      expect(page).to have_css('.free-button.selected')
    end

    it 'allows selecting premium plan' do
      find('.premium-button').click
      # In production, this would redirect to Stripe checkout
      # For now, just verify the button is clickable
      expect(page).to have_css('.premium-button')
    end
  end

  describe 'responsive design' do
    context 'on mobile devices' do
      before do
        page.driver.browser.manage.window.resize_to(375, 667)
        visit subscriptions_path
      end

      it 'stacks plan cards vertically' do
        cards = all('.plan-card')
        expect(cards.count).to eq(2)
        # Cards should be stacked vertically on mobile
      end

      it 'adjusts referral code section for mobile' do
        expect(page).to have_css('.referral-code-section')
        # Input and button should be full width on mobile
      end
    end
  end

  describe 'keyboard navigation' do
    before do
      visit subscriptions_path
    end

    it 'allows Enter key to apply referral code' do
      fill_in 'referral-code-input', with: 'FREEYEAR'
      find('#referral-code-input').send_keys(:enter)

      expect(page).to have_css('.referral-success-banner', visible: true)
    end
  end
end