# ABOUTME: Migrates existing user subscription data from User model to new Subscription model
# ABOUTME: Creates subscription records for existing users preserving their tier and expiration data
class MigrateUserSubscriptionData < ActiveRecord::Migration[7.0]
  def up
    # First ensure plans exist (in case this runs before seeds)
    ensure_plans_exist
    
    # Migrate existing user subscription data
    User.find_each do |user|
      next if user.subscriptions.exists? # Skip if already migrated
      
      # Determine the plan based on user's current subscription_tier
      plan = case user.subscription_tier
             when 'free'
               Plan.find_by!(tier: 'free')
             when 'premium'
               if user.subscription_expires_at.present? && user.subscription_expires_at > Time.current
                 # Active premium - determine if monthly or annual based on expiration
                 days_until_expiry = (user.subscription_expires_at - Time.current) / 1.day
                 if days_until_expiry > 45
                   Plan.find_by!(tier: 'premium', interval: 'year')
                 else
                   Plan.find_by!(tier: 'premium', interval: 'month')
                 end
               else
                 # Expired premium - default to monthly
                 Plan.find_by!(tier: 'premium', interval: 'month')
               end
             when 'pilot'
               Plan.find_by!(tier: 'pilot', interval: 'one_time')
             else
               Plan.find_by!(tier: 'free')
             end

      # Create subscription record
      subscription = Subscription.create!(
        user: user,
        plan: plan,
        status: determine_status(user),
        current_period_start: user.created_at,
        current_period_end: user.subscription_expires_at || calculate_period_end(plan, user.created_at),
        payment_provider: :manual,
        created_at: user.created_at,
        updated_at: user.updated_at
      )

      # If subscription is expired, mark it as canceled
      if user.subscription_expires_at.present? && user.subscription_expires_at <= Time.current
        subscription.update!(
          status: :canceled,
          canceled_at: user.subscription_expires_at,
          ended_at: user.subscription_expires_at
        )
      end
    end
    
    puts "Migrated #{User.count} users to new subscription model"
  end

  def down
    # This migration is not reversible as it would lose subscription history
    # However, the old columns are preserved so the system continues to work
    raise ActiveRecord::IrreversibleMigration
  end
  
  private
  
  def ensure_plans_exist
    return if Plan.exists?
    
    plans_data = [
      { name: "Free", tier: "free", interval: "one_time", price_cents: 0 },
      { name: "Premium Monthly", tier: "premium", interval: "month", price_cents: 999 },
      { name: "Premium Annual", tier: "premium", interval: "year", price_cents: 9999 },
      { name: "Premium Lifetime", tier: "premium", interval: "one_time", price_cents: 29999 },
      { name: "Pilot Monthly", tier: "pilot", interval: "month", price_cents: 0 },
      { name: "Pilot Annual", tier: "pilot", interval: "year", price_cents: 0 },
      { name: "Pilot Lifetime", tier: "pilot", interval: "one_time", price_cents: 0 }
    ]
    
    plans_data.each do |plan_data|
      Plan.find_or_create_by!(name: plan_data[:name]) do |plan|
        plan.tier = plan_data[:tier]
        plan.interval = plan_data[:interval]
        plan.price_cents = plan_data[:price_cents]
        plan.active = true
      end
    end
  end
  
  def determine_status(user)
    if user.subscription_tier == 'pilot'
      :active # Pilot is always active
    elsif user.subscription_tier == 'free'
      :active # Free is active
    elsif user.subscription_expires_at.present? && user.subscription_expires_at > Time.current
      :active # Active premium
    else
      :canceled # Expired or no expiration date
    end
  end
  
  def calculate_period_end(plan, start_date)
    case plan.interval
    when 'month'
      start_date + 1.month
    when 'year'
      start_date + 1.year
    when 'one_time'
      start_date + 100.years # Effectively never expires
    else
      start_date + 1.month
    end
  end
end