# spec/factories/users.rb
FactoryBot.define do
  factory :user do
    sequence(:email) { |n| "user#{n}@example.com" }
    password { "password123" }
    password_confirmation { "password123" }
    approved { true }
    confirmed_at { Time.current }
    
    trait :admin do
      role { 'admin' }
    end
    
    trait :super_boss do
      role { 'super_boss' }
    end

    trait :pilot do
      subscription_tier { 'pilot' }
    end
    
    # Create user profile automatically with complete data
    after(:create) do |user|
      unless user.user_profile&.last_name.present?
        # If profile was auto-created by model with empty last_name, update it
        if user.user_profile
          user.user_profile.update!(
            first_name: "Test",
            last_name: "User",
            city: "Bratislava",
            country: "Slovakia"
          )
        else
          create(:user_profile, user: user)
        end
      end
    end
  end
end